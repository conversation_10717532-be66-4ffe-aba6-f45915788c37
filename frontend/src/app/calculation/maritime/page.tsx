'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import { calculationAPI } from '@/lib/api';

interface MaritimeCalculationData {
  vesselName: string;
  grt: number;
  dwt: number;
  loa: number;
  beam: number;
  draft: number;
  portOfLoading: string;
  portOfDischarge: string;
  cargoType: string;
  cargoWeight: number;
  isDangerous: boolean;
  currency: string;
}

interface CalculationResult {
  pilotage?: any;
  tugboat?: any;
  portDues?: any;
  agency?: any;
  total?: number;
  currency?: string;
}

export default function MaritimeCalculation() {
  const [formData, setFormData] = useState<MaritimeCalculationData>({
    vesselName: '',
    grt: 0,
    dwt: 0,
    loa: 0,
    beam: 0,
    draft: 0,
    portOfLoading: '',
    portOfDischarge: '',
    cargoType: '',
    cargoWeight: 0,
    isDangerous: false,
    currency: 'USD'
  });

  const [result, setResult] = useState<CalculationResult | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [saving, setSaving] = useState(false);
  const router = useRouter();

  useEffect(() => {
    // Check if user is authenticated
    const token = document.cookie.split(';').find(cookie => cookie.trim().startsWith('token='));
    if (!token) {
      router.push('/login');
      return;
    }
  }, [router]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value, type } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? (e.target as HTMLInputElement).checked : 
              type === 'number' ? parseFloat(value) || 0 : value
    }));
  };

  const handleCalculate = async (e: React.FormEvent) => {
    e.preventDefault();
    setError('');
    setLoading(true);

    try {
      const response = await calculationAPI.calculateMaritime(formData);
      setResult(response.data);
    } catch (err: any) {
      console.error('Calculation error:', err);
      setError(err.message || 'Calculation failed');
    } finally {
      setLoading(false);
    }
  };

  const handleSave = async () => {
    if (!result) return;
    
    setSaving(true);
    try {
      await calculationAPI.saveCalculation({
        title: `${formData.vesselName} - Maritime Calculation`,
        type: 'maritime',
        input: formData,
        result: result
      });
      alert('Calculation saved successfully!');
    } catch (err: any) {
      console.error('Save error:', err);
      alert('Failed to save calculation: ' + (err.message || 'Unknown error'));
    } finally {
      setSaving(false);
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      {/* Header */}
      <header className="bg-white dark:bg-gray-800 shadow">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <div>
              <Link href="/dashboard" className="text-blue-600 hover:text-blue-500 text-sm">
                ← Back to Dashboard
              </Link>
              <h1 className="text-3xl font-bold text-gray-900 dark:text-white mt-2">
                Maritime Proforma Calculation
              </h1>
            </div>
          </div>
        </div>
      </header>

      <main className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        <div className="px-4 py-6 sm:px-0">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            {/* Calculation Form */}
            <div className="bg-white dark:bg-gray-800 shadow rounded-lg">
              <div className="px-4 py-5 sm:p-6">
                <h3 className="text-lg leading-6 font-medium text-gray-900 dark:text-white mb-4">
                  Vessel & Cargo Information
                </h3>
                
                {error && (
                  <div className="mb-4 bg-red-50 dark:bg-red-900/30 border-l-4 border-red-500 p-4">
                    <p className="text-sm text-red-700 dark:text-red-400">{error}</p>
                  </div>
                )}

                <form onSubmit={handleCalculate} className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                        Vessel Name
                      </label>
                      <input
                        type="text"
                        name="vesselName"
                        value={formData.vesselName}
                        onChange={handleInputChange}
                        required
                        className="mt-1 block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                        GRT (Gross Tonnage)
                      </label>
                      <input
                        type="number"
                        name="grt"
                        value={formData.grt}
                        onChange={handleInputChange}
                        required
                        min="0"
                        className="mt-1 block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                        DWT (Deadweight)
                      </label>
                      <input
                        type="number"
                        name="dwt"
                        value={formData.dwt}
                        onChange={handleInputChange}
                        required
                        min="0"
                        className="mt-1 block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                        LOA (Length Overall) - meters
                      </label>
                      <input
                        type="number"
                        name="loa"
                        value={formData.loa}
                        onChange={handleInputChange}
                        required
                        min="0"
                        step="0.1"
                        className="mt-1 block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                        Beam - meters
                      </label>
                      <input
                        type="number"
                        name="beam"
                        value={formData.beam}
                        onChange={handleInputChange}
                        required
                        min="0"
                        step="0.1"
                        className="mt-1 block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                        Draft - meters
                      </label>
                      <input
                        type="number"
                        name="draft"
                        value={formData.draft}
                        onChange={handleInputChange}
                        required
                        min="0"
                        step="0.1"
                        className="mt-1 block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                        Port of Loading
                      </label>
                      <input
                        type="text"
                        name="portOfLoading"
                        value={formData.portOfLoading}
                        onChange={handleInputChange}
                        required
                        className="mt-1 block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                        Port of Discharge
                      </label>
                      <input
                        type="text"
                        name="portOfDischarge"
                        value={formData.portOfDischarge}
                        onChange={handleInputChange}
                        required
                        className="mt-1 block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                        Cargo Type
                      </label>
                      <input
                        type="text"
                        name="cargoType"
                        value={formData.cargoType}
                        onChange={handleInputChange}
                        required
                        className="mt-1 block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                        Cargo Weight (MT)
                      </label>
                      <input
                        type="number"
                        name="cargoWeight"
                        value={formData.cargoWeight}
                        onChange={handleInputChange}
                        required
                        min="0"
                        className="mt-1 block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                        Currency
                      </label>
                      <select
                        name="currency"
                        value={formData.currency}
                        onChange={handleInputChange}
                        className="mt-1 block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                      >
                        <option value="USD">USD</option>
                        <option value="EUR">EUR</option>
                        <option value="TRY">TRY</option>
                      </select>
                    </div>

                    <div className="flex items-center">
                      <input
                        type="checkbox"
                        name="isDangerous"
                        checked={formData.isDangerous}
                        onChange={handleInputChange}
                        className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                      />
                      <label className="ml-2 block text-sm text-gray-700 dark:text-gray-300">
                        Dangerous Cargo
                      </label>
                    </div>
                  </div>

                  <div className="pt-4">
                    <button
                      type="submit"
                      disabled={loading}
                      className="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"
                    >
                      {loading ? 'Calculating...' : 'Calculate Proforma'}
                    </button>
                  </div>
                </form>
              </div>
            </div>

            {/* Results */}
            {result && (
              <div className="bg-white dark:bg-gray-800 shadow rounded-lg">
                <div className="px-4 py-5 sm:p-6">
                  <div className="flex justify-between items-center mb-4">
                    <h3 className="text-lg leading-6 font-medium text-gray-900 dark:text-white">
                      Calculation Results
                    </h3>
                    <button
                      onClick={handleSave}
                      disabled={saving}
                      className="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 disabled:opacity-50"
                    >
                      {saving ? 'Saving...' : 'Save Calculation'}
                    </button>
                  </div>

                  <div className="space-y-4">
                    {result.pilotage && (
                      <div className="border-l-4 border-blue-500 pl-4">
                        <h4 className="font-medium text-gray-900 dark:text-white">Pilotage</h4>
                        <p className="text-sm text-gray-600 dark:text-gray-400">
                          {result.pilotage.calculation}
                        </p>
                        <p className="text-lg font-semibold text-blue-600">
                          {result.pilotage.total} {result.currency}
                        </p>
                      </div>
                    )}

                    {result.tugboat && (
                      <div className="border-l-4 border-green-500 pl-4">
                        <h4 className="font-medium text-gray-900 dark:text-white">Tugboat</h4>
                        <p className="text-sm text-gray-600 dark:text-gray-400">
                          {result.tugboat.calculation}
                        </p>
                        <p className="text-lg font-semibold text-green-600">
                          {result.tugboat.total} {result.currency}
                        </p>
                      </div>
                    )}

                    {result.portDues && (
                      <div className="border-l-4 border-yellow-500 pl-4">
                        <h4 className="font-medium text-gray-900 dark:text-white">Port Dues</h4>
                        <p className="text-sm text-gray-600 dark:text-gray-400">
                          {result.portDues.calculation}
                        </p>
                        <p className="text-lg font-semibold text-yellow-600">
                          {result.portDues.total} {result.currency}
                        </p>
                      </div>
                    )}

                    {result.agency && (
                      <div className="border-l-4 border-purple-500 pl-4">
                        <h4 className="font-medium text-gray-900 dark:text-white">Agency Fee</h4>
                        <p className="text-sm text-gray-600 dark:text-gray-400">
                          {result.agency.calculation}
                        </p>
                        <p className="text-lg font-semibold text-purple-600">
                          {result.agency.total} {result.currency}
                        </p>
                      </div>
                    )}

                    {result.total && (
                      <div className="border-t pt-4 mt-4">
                        <div className="flex justify-between items-center">
                          <h4 className="text-xl font-bold text-gray-900 dark:text-white">
                            Total Amount
                          </h4>
                          <p className="text-2xl font-bold text-blue-600">
                            {result.total} {result.currency}
                          </p>
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      </main>
    </div>
  );
}
