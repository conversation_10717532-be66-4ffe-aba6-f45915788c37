'use client';

import { useEffect } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';

export default function Home() {
  const router = useRouter();

  useEffect(() => {
    // Check if user is already logged in
    const token = document.cookie.split(';').find(cookie => cookie.trim().startsWith('token='));
    if (token) {
      router.push('/dashboard');
    }
  }, [router]);

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-900 dark:to-gray-800">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex flex-col justify-center min-h-screen py-12">
          {/* Header */}
          <div className="text-center">
            <h1 className="text-4xl font-bold text-gray-900 dark:text-white sm:text-5xl md:text-6xl">
              <span className="block">QuickPDA</span>
              <span className="block text-blue-600 dark:text-blue-400">Maritime Calculations</span>
            </h1>
            <p className="mt-3 max-w-md mx-auto text-base text-gray-500 dark:text-gray-400 sm:text-lg md:mt-5 md:text-xl md:max-w-3xl">
              Professional maritime proforma calculations for port dues, pilotage, tugboat services, and agency fees.
            </p>
          </div>

          {/* Features */}
          <div className="mt-16">
            <div className="grid grid-cols-1 gap-8 sm:grid-cols-2 lg:grid-cols-3">
              <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
                <div className="flex items-center justify-center h-12 w-12 rounded-md bg-blue-500 text-white mx-auto">
                  <span className="text-xl">⚓</span>
                </div>
                <h3 className="mt-4 text-lg font-medium text-gray-900 dark:text-white text-center">
                  Maritime Calculations
                </h3>
                <p className="mt-2 text-base text-gray-500 dark:text-gray-400 text-center">
                  Accurate proforma calculations for all maritime services including pilotage, tugboat, and port dues.
                </p>
              </div>

              <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
                <div className="flex items-center justify-center h-12 w-12 rounded-md bg-green-500 text-white mx-auto">
                  <span className="text-xl">💰</span>
                </div>
                <h3 className="mt-4 text-lg font-medium text-gray-900 dark:text-white text-center">
                  Cost Management
                </h3>
                <p className="mt-2 text-base text-gray-500 dark:text-gray-400 text-center">
                  Track and manage all your maritime costs with detailed breakdowns and historical data.
                </p>
              </div>

              <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
                <div className="flex items-center justify-center h-12 w-12 rounded-md bg-purple-500 text-white mx-auto">
                  <span className="text-xl">📊</span>
                </div>
                <h3 className="mt-4 text-lg font-medium text-gray-900 dark:text-white text-center">
                  Professional Reports
                </h3>
                <p className="mt-2 text-base text-gray-500 dark:text-gray-400 text-center">
                  Generate professional proforma reports for your clients and stakeholders.
                </p>
              </div>
            </div>
          </div>

          {/* CTA */}
          <div className="mt-16 text-center">
            <div className="space-y-4 sm:space-y-0 sm:space-x-4 sm:flex sm:justify-center">
              <Link
                href="/register"
                className="inline-flex items-center justify-center px-8 py-3 border border-transparent text-base font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 md:py-4 md:text-lg md:px-10 transition-colors"
              >
                Get Started
              </Link>
              <Link
                href="/login"
                className="inline-flex items-center justify-center px-8 py-3 border border-gray-300 dark:border-gray-600 text-base font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 md:py-4 md:text-lg md:px-10 transition-colors"
              >
                Sign In
              </Link>
            </div>
          </div>

          {/* Footer */}
          <div className="mt-16 text-center">
            <p className="text-sm text-gray-500 dark:text-gray-400">
              Professional maritime calculation software for shipping industry professionals.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}
